# Step-by-Step Analysis Guide

This guide provides detailed workflows for each type of analysis available in the application.

## Prerequisites

1. **Start the Application**
   ```bash
   cd backend
   python run.py
   ```

2. **Access the API**
   - Base URL: `http://localhost:7000`
   - Swagger UI: `http://localhost:7000/api/docs`

---

## 🔄 General Workflow

### Step 1: Create Session and Upload File

```bash
# Upload a file (creates session automatically)
curl -X POST http://localhost:7000/api/upload \
  -F "file=@your_data.xlsx" \
  -c cookies.txt -b cookies.txt
```

**Response:**
```json
{
  "success": true,
  "filename": "your_data.xlsx",
  "columns": ["id", "text", "category"],
  "session_id": "abc123def4"
}
```

### Step 2: (Optional) Process File with NLP

```bash
# Process file with Korean text analysis
curl -X POST http://localhost:7000/api/process \
  -H "Content-Type: application/json" \
  -d '{
    "column_name": "text",
    "language": "korean",
    "analyzer": "mecab",
    "pos_tags": ["NNG", "NNP"],
    "remove_stopwords": true,
    "min_length": 2
  }' \
  -b cookies.txt
```

---

## 📊 Analysis Type 1: Frequency Analysis

### Step 1: Get Word Data for Manual Selection

```bash
curl -X POST http://localhost:7000/api/analyse/analyze \
  -F "column_name=text" \
  -F "filename=your_data.xlsx" \
  -b cookies.txt
```

**Response:**
```json
{
  "success": true,
  "word_data": [
    {"word": "분석", "count": 45},
    {"word": "데이터", "count": 32},
    {"word": "연구", "count": 28}
  ],
  "total_words": 1500
}
```

### Step 2: Generate WordCloud with Selected Words

```bash
curl -X POST http://localhost:7000/api/analyse/wordcloud \
  -F "column_name=text" \
  -F "filename=your_data.xlsx" \
  -F "selection_type=manual" \
  -F "max_words=50" \
  -F "cloud_shape=circle" \
  -F "cloud_color=viridis" \
  -F 'selected_words=[{"word":"분석","count":45},{"word":"데이터","count":32}]' \
  -b cookies.txt
```

### Step 3: Download Results

```bash
# Download word cloud image
curl -X GET "http://localhost:7000/api/files/wordcloud.png" \
  -b cookies.txt -o wordcloud.png

# Download frequency analysis Excel file
curl -X GET "http://localhost:7000/api/files/frequency_analysis.xlsx?download=true" \
  -b cookies.txt -o frequency_results.xlsx
```

---

## 🎯 Analysis Type 2: LDA Topic Modeling

### Step 1: Run LDA Analysis

```bash
curl -X POST http://localhost:7000/api/analyse/process \
  -H "Content-Type: application/json" \
  -d '{
    "text_column": "text",
    "min_topic": 3,
    "max_topic": 8,
    "no_below": 5,
    "no_above": 0.3,
    "network_style": "academic",
    "chart_style": "default",
    "fast_mode": false
  }' \
  -b cookies.txt
```

**Response:**
```json
{
  "success": true,
  "optimal_topics": 5,
  "topics": [
    {
      "topic_id": 0,
      "keywords": ["분석", "데이터", "연구"],
      "weights": [0.15, 0.12, 0.10]
    }
  ],
  "pyldavis_html": "lda_visualization.html",
  "network_img_url": "/api/files/topic_network.png",
  "csv_download_url": "/api/files/lda_results.csv?download=true"
}
```

### Step 2: (Optional) Edit Keywords

```bash
curl -X POST http://localhost:7000/api/analyse/edit_keywords \
  -H "Content-Type: application/json" \
  -d '{
    "edited_keywords": {
      "0": ["분석", "데이터분석", "통계"],
      "1": ["연구", "학술", "논문"]
    },
    "chart_style": "academic"
  }' \
  -b cookies.txt
```

### Step 3: Download Results

```bash
# Download LDA results CSV
curl -X GET "http://localhost:7000/api/files/lda_results.csv?download=true" \
  -b cookies.txt -o lda_results.csv

# Download topic network visualization
curl -X GET "http://localhost:7000/api/files/topic_network.png" \
  -b cookies.txt -o topic_network.png
```

---

## 📈 Analysis Type 3: TF-IDF Analysis

### Step 1: Get TF-IDF Word Data

```bash
curl -X POST http://localhost:7000/api/tfidf/analyze \
  -F "column_name=text" \
  -b cookies.txt
```

**Response:**
```json
{
  "success": true,
  "word_data": [
    {"word": "특별한", "tfidf_score": 0.85},
    {"word": "독특한", "tfidf_score": 0.72}
  ],
  "download_url": "/api/files/tfidf_results.xlsx?download=true"
}
```

### Step 2: Generate TF-IDF WordCloud

```bash
curl -X POST http://localhost:7000/api/tfidf/wordcloud \
  -F "column_name=text" \
  -F "selection_type=top_n" \
  -F "max_words=50" \
  -F "cloud_shape=rectangle" \
  -F "cloud_color=plasma" \
  -b cookies.txt
```

---

## 🔗 Analysis Type 4: N-gram Analysis

### Step 1: Get N-gram Data

```bash
curl -X POST http://localhost:7000/api/ngram/get_word_data \
  -F "column_name=text" \
  -F "n_gram=2" \
  -F "max_features=3000" \
  -b cookies.txt
```

**Response:**
```json
{
  "success": true,
  "word_data": [
    {"ngram": "데이터 분석", "count": 25},
    {"ngram": "기계 학습", "count": 18}
  ]
}
```

### Step 2: Generate N-gram WordCloud

```bash
curl -X POST http://localhost:7000/api/ngram/generate_wordcloud \
  -F 'selected_words=[{"ngram":"데이터 분석","count":25}]' \
  -F "cloud_shape=circle" \
  -F "cloud_color=viridis" \
  -F "n_gram=2" \
  -b cookies.txt
```

---

## 🎭 Analysis Type 5: Sentiment & Similarity Analysis

### Step 1: Get Available Models

```bash
curl -X GET http://localhost:7000/api/sentrans/get_models \
  -b cookies.txt
```

### Step 2: Run Similarity Analysis

```bash
curl -X POST http://localhost:7000/api/sentrans/analyze \
  -F "column_name=text" \
  -F "model_name=sentence-transformers/xlm-r-100langs-bert-base-nli-stsb-mean-tokens" \
  -F "top_n=20" \
  -F "similarity_threshold=0.5" \
  -F "clustering_method=kmeans" \
  -F "n_clusters=5" \
  -b cookies.txt
```

---

## 🕸️ Analysis Type 6: Connectivity Analysis

### Step 1: Run Connectivity Analysis

```bash
curl -X POST http://localhost:7000/api/connet/analyze \
  -F "column_name=text" \
  -F "top_word_count=50" \
  -F "node_size_min=10" \
  -F "node_size_max=100" \
  -F "edge_width_min=1" \
  -F "edge_width_max=5" \
  -F "layout_seed=42" \
  -F "use_concor=true" \
  -F "color_palette=Set3" \
  -b cookies.txt
```

---

## 🧹 Cleanup

### Clear Current Session

```bash
curl -X POST http://localhost:7000/api/clear-session \
  -b cookies.txt
```

### Clear All Sessions (Admin)

```bash
curl -X POST "http://localhost:7000/api/clear-session?all=true" \
  -b cookies.txt
```

---

## 📱 Frontend Integration Examples

### JavaScript/React Example

```javascript
// Upload file
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
    credentials: 'include' // Important for session cookies
  });
  
  return await response.json();
};

// Get word data for frequency analysis
const getWordData = async (columnName, filename) => {
  const formData = new FormData();
  formData.append('column_name', columnName);
  formData.append('filename', filename);
  
  const response = await fetch('/api/analyse/analyze', {
    method: 'POST',
    body: formData,
    credentials: 'include'
  });
  
  return await response.json();
};

// Generate wordcloud
const generateWordcloud = async (selectedWords, options) => {
  const formData = new FormData();
  formData.append('selected_words', JSON.stringify(selectedWords));
  formData.append('cloud_shape', options.shape || 'rectangle');
  formData.append('cloud_color', options.color || 'viridis');
  
  const response = await fetch('/api/analyse/wordcloud', {
    method: 'POST',
    body: formData,
    credentials: 'include'
  });
  
  return await response.json();
};
```

### Python Client Example

```python
import requests
import json

# Create session
session = requests.Session()

# Upload file
with open('data.xlsx', 'rb') as f:
    response = session.post(
        'http://localhost:7000/api/upload',
        files={'file': f}
    )
    upload_result = response.json()

# Run frequency analysis
response = session.post(
    'http://localhost:7000/api/analyse/analyze',
    data={
        'column_name': 'text',
        'filename': upload_result['filename']
    }
)
word_data = response.json()

# Generate wordcloud
selected_words = word_data['word_data'][:20]  # Top 20 words
response = session.post(
    'http://localhost:7000/api/analyse/wordcloud',
    data={
        'selected_words': json.dumps(selected_words),
        'cloud_shape': 'circle',
        'cloud_color': 'viridis'
    }
)
wordcloud_result = response.json()

# Download wordcloud
if 'wordcloud_url' in wordcloud_result:
    img_response = session.get(
        f"http://localhost:7000{wordcloud_result['wordcloud_url']}"
    )
    with open('wordcloud.png', 'wb') as f:
        f.write(img_response.content)
```

---

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure you're accessing via the same host (localhost vs 127.0.0.1)
2. **Session Issues**: Always include cookies/credentials in requests
3. **File Not Found**: Check that files are uploaded before analysis
4. **Memory Issues**: Use `fast_mode=true` for LDA analysis with large datasets

### Debug Endpoints

```bash
# Check session status
curl -X GET http://localhost:7000/api/session-status -b cookies.txt

# Get application info
curl -X GET http://localhost:7000/

# Check if file exists
curl -X GET http://localhost:7000/api/files/your_file.xlsx -b cookies.txt
```
