# API Quick Reference Guide

## 🚀 Quick Start

```bash
# 1. Upload file
curl -X POST http://localhost:7000/api/upload -F "file=@data.xlsx" -c cookies.txt

# 2. Get word data
curl -X POST http://localhost:7000/api/analyse/analyze -F "column_name=text" -F "filename=data.xlsx" -b cookies.txt

# 3. Generate wordcloud
curl -X POST http://localhost:7000/api/analyse/wordcloud -F "selected_words=[...]" -b cookies.txt
```

---

## 📋 Endpoint Summary

| Category | Endpoint | Method | Purpose |
|----------|----------|--------|---------|
| **Upload** | `/api/upload` | POST | Upload file |
| **Process** | `/api/process` | POST | NLP processing |
| **Frequency** | `/api/analyse/analyze` | POST | Get word frequencies |
| **Frequency** | `/api/analyse/wordcloud` | POST | Generate frequency wordcloud |
| **LDA** | `/api/analyse/process` | POST | LDA topic modeling |
| **LDA** | `/api/analyse/edit_keywords` | POST | Edit LDA keywords |
| **TF-IDF** | `/api/tfidf/analyze` | POST | TF-IDF analysis |
| **TF-IDF** | `/api/tfidf/wordcloud` | POST | TF-IDF wordcloud |
| **N-gram** | `/api/ngram/get_word_data` | POST | Get n-gram data |
| **N-gram** | `/api/ngram/analyze` | POST | Full n-gram analysis |
| **N-gram** | `/api/ngram/generate_wordcloud` | POST | N-gram wordcloud |
| **Sentiment** | `/api/sentrans/analyze` | POST | Similarity analysis |
| **Sentiment** | `/api/sentrans/get_models` | GET | Available models |
| **Network** | `/api/connet/analyze` | POST | Connectivity analysis |
| **Files** | `/api/files/{path}` | GET | Serve/download files |
| **Session** | `/api/session-status` | GET | Session info |
| **Session** | `/api/clear-session` | POST | Clear session |

---

## 🔧 Common Parameters

### File Upload
```bash
-F "file=@filename.xlsx"
```

### Column Selection
```bash
-F "column_name=text_column"
```

### WordCloud Options
```bash
-F "cloud_shape=rectangle"     # rectangle, circle
-F "cloud_color=viridis"       # viridis, plasma, inferno, magma
-F "max_words=50"              # number of words
```

### Selection Types
```bash
-F "selection_type=top_n"      # top_n, manual
-F "selected_words=[...]"      # JSON array for manual
```

---

## 📊 Analysis Workflows

### 1. Frequency Analysis
```
Upload → Get Word Data → Generate WordCloud → Download
  ↓           ↓              ↓              ↓
/upload → /analyse/analyze → /analyse/wordcloud → /files/...
```

### 2. LDA Topic Modeling
```
Upload → Process → LDA Analysis → (Edit Keywords) → Download
  ↓        ↓          ↓              ↓              ↓
/upload → /process → /analyse/process → /analyse/edit_keywords → /files/...
```

### 3. TF-IDF Analysis
```
Upload → TF-IDF Analysis → Generate WordCloud → Download
  ↓           ↓                ↓              ↓
/upload → /tfidf/analyze → /tfidf/wordcloud → /files/...
```

### 4. N-gram Analysis
```
Upload → Get N-gram Data → Generate WordCloud → Download
  ↓           ↓                ↓              ↓
/upload → /ngram/get_word_data → /ngram/generate_wordcloud → /files/...
```

---

## 🎯 Response Formats

### Success Response
```json
{
  "success": true,
  "data": "...",
  "additional_fields": "..."
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message"
}
```

### Word Data Format
```json
{
  "word_data": [
    {"word": "example", "count": 45},
    {"word": "analysis", "count": 32}
  ]
}
```

### File URLs
```json
{
  "wordcloud_url": "/api/files/wordcloud.png",
  "download_url": "/api/files/results.xlsx?download=true"
}
```

---

## 🔐 Authentication

All endpoints use **session-based authentication**:

```bash
# Save cookies on first request
curl ... -c cookies.txt

# Use cookies on subsequent requests
curl ... -b cookies.txt
```

For JavaScript/Frontend:
```javascript
fetch('/api/endpoint', {
  credentials: 'include'  // Include session cookies
})
```

---

## 📁 File Management

### Download Files
```bash
# View file
curl "http://localhost:7000/api/files/image.png" -b cookies.txt

# Download file
curl "http://localhost:7000/api/files/results.xlsx?download=true" -b cookies.txt -o results.xlsx
```

### File Types
- **Images**: `.png`, `.jpg` (wordclouds, visualizations)
- **Data**: `.xlsx`, `.csv` (analysis results)
- **HTML**: `.html` (interactive visualizations)

---

## 🛠️ Development Tips

### 1. Session Management
```bash
# Check session status
curl -X GET http://localhost:7000/api/session-status -b cookies.txt

# Clear session when done
curl -X POST http://localhost:7000/api/clear-session -b cookies.txt
```

### 2. Error Handling
```javascript
const response = await fetch('/api/endpoint', options);
const data = await response.json();

if (!data.success) {
  console.error('API Error:', data.error);
  return;
}
```

### 3. File Upload with Progress
```javascript
const formData = new FormData();
formData.append('file', file);

const xhr = new XMLHttpRequest();
xhr.upload.onprogress = (e) => {
  const percent = (e.loaded / e.total) * 100;
  console.log(`Upload: ${percent}%`);
};

xhr.open('POST', '/api/upload');
xhr.send(formData);
```

### 4. Batch Operations
```bash
# Process multiple analyses in sequence
curl -X POST .../upload -F "file=@data.xlsx" -c cookies.txt
curl -X POST .../analyse/analyze -F "column_name=text" -b cookies.txt
curl -X POST .../tfidf/analyze -F "column_name=text" -b cookies.txt
curl -X POST .../ngram/get_word_data -F "column_name=text" -b cookies.txt
```

---

## 🐛 Debugging

### Check Application Status
```bash
curl -X GET http://localhost:7000/
```

### Validate File Upload
```bash
curl -X GET http://localhost:7000/api/session-status -b cookies.txt
```

### Test File Access
```bash
curl -X GET http://localhost:7000/api/files/test.txt -b cookies.txt
```

### Common HTTP Status Codes
- `200`: Success
- `400`: Bad Request (check parameters)
- `404`: Not Found (file/session missing)
- `500`: Server Error (check logs)

---

## 📚 Advanced Usage

### Custom Analysis Pipeline
```python
import requests
import json

session = requests.Session()
base_url = "http://localhost:7000"

# 1. Upload and process
upload_resp = session.post(f"{base_url}/api/upload", files={'file': open('data.xlsx', 'rb')})
process_resp = session.post(f"{base_url}/api/process", json={
    "column_name": "text",
    "language": "korean",
    "analyzer": "mecab"
})

# 2. Run multiple analyses
analyses = [
    ("frequency", "/api/analyse/analyze"),
    ("tfidf", "/api/tfidf/analyze"),
    ("ngram", "/api/ngram/get_word_data")
]

results = {}
for name, endpoint in analyses:
    resp = session.post(f"{base_url}{endpoint}", data={"column_name": "text"})
    results[name] = resp.json()

# 3. Generate visualizations
for name, data in results.items():
    if 'word_data' in data:
        wordcloud_resp = session.post(f"{base_url}/api/{name}/wordcloud", data={
            "selected_words": json.dumps(data['word_data'][:20])
        })
```

### Environment-Specific Configuration
```bash
# Development
export API_BASE_URL=http://localhost:7000

# Production
export API_BASE_URL=https://your-app.com
```

---

## 🔗 Related Documentation

- [Complete API Documentation](API_ENDPOINTS_DOCUMENTATION.md)
- [Step-by-Step Guide](STEP_BY_STEP_ANALYSIS_GUIDE.md)
- [CORS Configuration](CORS_DEPLOYMENT.md)
- [Session Management](SESSION_MANAGEMENT.md)
